import time
import os
import sys
import math

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

from machine import FPIOA
from machine import Pin
from machine import UART

# 导入cv_lite库以提升性能 / Import cv_lite for performance optimization
import cv_lite
import ulab.numpy as np

sensor = None

# --- MODIFICATION START ---
# 将竞赛流程设置为1，以直接测试矩形识别
contest_flow = 0

# 用于存储和计算平均值的新变量
recognition_count = 0
midpoints_history = []
AVERAGE_COUNT = 5

# 激光点运动控制相关变量
current_target_index = 0  # 当前目标点索引
all_target_points = []    # 所有40个目标点（每边10等分）
target_threshold = 5    # 到达目标点的阈值距离
motion_active = False     # 运动控制需要等待矩形识别完成

# 矩形识别状态变量
rectangle_detected = False  # 矩形是否已检测完成
final_inner_points = None   # 最终确定的内框角点
final_outer_points = None   # 最终确定的外框角点
final_midpoints = None
# --- MODIFICATION END ---

roi = (233, 71, 319, 340)

target_str = "Get Coord\r\n"

# Variables for smoothing
prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}
alpha = 1 # Smoothing factor



def calculate_polygon_area(corners):
    """计算多边形面积（用于判断内外框）"""
    if len(corners) < 3:
        return 0

    area = 0
    n = len(corners)
    for i in range(n):
        j = (i + 1) % n
        area += corners[i][0] * corners[j][1]
        area -= corners[j][0] * corners[i][1]
    return abs(area) / 2

def sort_corners_by_position(corners):
    """按位置排序角点：左上、右上、右下、左下"""
    if len(corners) != 4:
        return corners

    corners = list(corners)  # 确保是列表

    # 优化的几何排序方法：先按Y坐标分上下，再按X坐标分左右
    # 1. 按Y坐标排序，分出上下两组
    corners_by_y = sorted(corners, key=lambda p: p[1])
    top_two = corners_by_y[:2]  # Y坐标较小的两个点（上方）
    bottom_two = corners_by_y[2:]  # Y坐标较大的两个点（下方）

    # 2. 在上方两点中，按X坐标排序：左上、右上
    top_sorted = sorted(top_two, key=lambda p: p[0])
    top_left = top_sorted[0]  # 左上角
    top_right = top_sorted[1]  # 右上角

    # 3. 在下方两点中，按X坐标排序：左下、右下
    bottom_sorted = sorted(bottom_two, key=lambda p: p[0])
    bottom_left = bottom_sorted[0]  # 左下角
    bottom_right = bottom_sorted[1]  # 右下角

    # 4. 按顺时针顺序返回：左上、右上、右下、左下
    return [top_left, top_right, bottom_right, bottom_left]

def generate_target_points(midpoints):
    """根据四个中点生成矩形的40个目标点（每边10等分）"""
    if len(midpoints) != 4:
        return []

    all_points = []
    # 四个中点按顺序：[左上, 右上, 右下, 左下] - 从左上角开始顺时针
    for i in range(4):
        start_point = midpoints[i]
        end_point = midpoints[(i + 1) % 4]

        # 每条边10等分，生成10个点（不包括起点，包括终点）
        for j in range(1, 13):
            t = j / 13.0  # 插值参数
            x = int(start_point[0] + t * (end_point[0] - start_point[0]))
            y = int(start_point[1] + t * (end_point[1] - start_point[1]))
            all_points.append((x, y))

    return all_points

def send_red_diff(uart2, rlaser_x, rlaser_y, target_x, target_y):
    rdx = rlaser_x - target_x
    rdy = rlaser_y - target_y
    # 处理dx数据
    if rdx >= 0:
        rdx_sign = 0x00  # 正数符号位
    else:
        rdx_sign = 0x01  # 负数符号位
        rdx = abs(rdx)    # 取绝对值
    # 限制数据范围到0-255
    rdx_data = min(abs(rdx), 255)
    # 处理dy数据
    if rdy >= 0:
        rdy_sign = 0x00  # 正数符号位
    else:
        rdy_sign = 0x01  # 负数符号位
        rdy = abs(rdy)    # 取绝对值
    # 限制数据范围到0-255
    rdy_data = min(abs(rdy), 255)
    # 构建帧数据 - 逐字节发送，确保数据完整性
    frame_data = [0x44, rdx_sign, rdx_data, rdy_sign, rdy_data, 0x55]
    # 调试信息
    print(f"串口2发送差值帧: 激光({rlaser_x},{rlaser_y}) 目标({target_x},{target_y}) 差值({rdx},{rdy}) 帧:{[hex(b) for b in frame_data]}")
    # 逐字节发送，确保每个字节都正确传输
    for byte_val in frame_data:
        uart2.write(bytes([byte_val]))
        time.sleep_ms(1)  # 每字节间隔1ms

    return rdx, rdy

def red_motion(laser_x, laser_y):
    """处理红色激光点运动控制"""
    global current_target_index, all_target_points

    if not all_target_points:
        return None

    # 获取当前目标点
    current_target = all_target_points[current_target_index]
    target_x, target_y = current_target
    #img.draw_circle(target_x, target_y, 2, color=(255, 0, 0), thickness=2, fill=True)
    #print(f"current_target{target_x,target_y}")
    # 计算差值并发送
    dx, dy = send_red_diff(uart2, laser_x, laser_y, target_x, target_y)
    # 计算距离
    distance = math.sqrt(dx*dx + dy*dy)

    # 检查是否到达目标点
    if distance <= target_threshold:
        current_target_index = (current_target_index + 1) % len(all_target_points)
        print(f"到达目标点，切换到目标点 {current_target_index}")

    return {
        'target_index': current_target_index,
        'target_point': current_target,
        'distance': distance,
        'diff': (dx, dy)
    }

def green_motion(rlaser_x,rlaser_y,glaser_x,glaser_y):
    dx, dy = send_green_diff(uart3,rlaser_x, rlaser_y,glaser_x,glaser_y)
    # 计算距离
    distance = math.sqrt(dx*dx + dy*dy)

    return {
        'distance': distance,
        'diff': (dx, dy)
    }
def send_green_diff(uart3,rlaser_x,rlaser_y,glaser_x,glaser_y):
    dx = glaser_x - rlaser_x
    dy = glaser_y - rlaser_y

    # 处理dx数据
    if dx >= 0:
        dx_sign = 0x00  # 正数符号位
    else:
        dx_sign = 0x01  # 负数符号位
        dx = abs(dx)  # 取绝对值
    # 限制数据范围到0-255
    dx_data = min(abs(dx), 255)
    # 处理dy数据
    if dy >= 0:
        dy_sign = 0x00  # 正数符号位
    else:
        dy_sign = 0x01  # 负数符号位
        dy = abs(dy)  # 取绝对值
    # 限制数据范围到0-255
    dy_data = min(abs(dy), 255)

    # 构建帧数据 - 逐字节发送，确保数据完整性
    frame_data = [0x22, dx_sign, dx_data, dy_sign, dy_data, 0x33]
    # 调试信息

    for byte_val in frame_data:
        uart3.write(bytes([byte_val]))
        time.sleep_ms(1)  # 每字节间隔1ms
        print(
            f"串口3发送差值帧: 绿色({glaser_x},{glaser_y}) 红色({rlaser_x},{rlaser_y}) 差值({dx},{dy}) 帧:{[hex(b) for b in frame_data]}")
    return dx, dy
def auto_detect_rectangle(img):
    """自动检测矩形框"""
    global recognition_count, midpoints_history, all_target_points, motion_active, current_target_index
    global rectangle_detected, final_inner_points, final_outer_points,final_midpoints

    try:
        # 进行矩形识别
        binary_img = img.binary([(6, 22, -10, 8, -17, 7)], invert=True, zero=True)
        binary_img.gaussian(2)

        #rects = binary_img.find_rects(roi=roi, threshold=5000)
        rects = binary_img.find_rects(roi=roi, threshold=1000)
        print("正在进行矩形检测")
    except Exception as e:
        print(f"图像处理异常: {e}")
        return []
    if len(rects) >= 2:
        try:
            first_rect = rects[0]
            second_rect = rects[1]

            # 安全获取角点
            first_corners = first_rect.corners()
            second_corners = second_rect.corners()

            # 检查角点有效性
            if not first_corners or not second_corners:
                print("角点检测失败：返回空列表")
                return []

            midpoints = []

            if len(first_corners) == 4 and len(second_corners) == 4:
                first_area = calculate_polygon_area(first_corners)
                second_area = calculate_polygon_area(second_corners)

            if first_area < second_area:
                inner_corners = first_corners
                outer_corners = second_corners
            else:
                inner_corners = second_corners
                outer_corners = first_corners

            inner_sorted = sort_corners_by_position(inner_corners)
            outer_sorted = sort_corners_by_position(outer_corners)

            # 安全检查：确保排序函数返回有效结果
            if not inner_sorted or not outer_sorted:
                print(f"角点排序失败: inner_sorted={inner_sorted}, outer_sorted={outer_sorted}")
                return []

            if len(inner_sorted) != 4 or len(outer_sorted) != 4:
                print(f"排序后角点数量错误: inner={len(inner_sorted)}, outer={len(outer_sorted)}")
                return []

            # 计算对应角点的中点
            for i in range(4):
                try:
                    inner_point = inner_sorted[i]
                    outer_point = outer_sorted[i]

                    # 检查角点是否有效
                    if not inner_point or not outer_point:
                        print(f"发现无效角点 i={i}: inner={inner_point}, outer={outer_point}")
                        return []

                    if len(inner_point) < 2 or len(outer_point) < 2:
                        print(f"角点坐标不完整 i={i}: inner={inner_point}, outer={outer_point}")
                        return []

                    inner_x, inner_y = inner_point
                    outer_x, outer_y = outer_point
                    mid_x = int((inner_x + outer_x) / 2)
                    mid_y = int((inner_y + outer_y) / 2)
                    midpoints.append((mid_x, mid_y))
                except Exception as e:
                    print(f"中点计算异常 i={i}: {e}")
                    return []
                #img.draw_circle(outer_x, outer_y, 2, color=(0, 0, 255), thickness=2, fill=True)
                #img.draw_circle(inner_x, inner_y, 2, color=(0, 255, 0), thickness=2, fill=True)

            print(f"内框角点: {inner_sorted}")
            print(f"外框角点: {outer_sorted}")
            print(f"中点坐标: {midpoints}")

            if len(midpoints) == 4:
                midpoints_history.append(midpoints)
                recognition_count += 1   #
                print(f"矩形检测进度: {recognition_count}/{AVERAGE_COUNT}")

                # 在图像上绘制当前识别到的矩形
                for i in range(4):
                    x1, y1 = midpoints[i]
                    x2, y2 = midpoints[(i + 1) % 4]
                    img.draw_line(x1, y1, x2, y2, color=(255, 0, 255), thickness=2)

                if recognition_count >= AVERAGE_COUNT:
                    # 计算平均中点
                    sum_midpoints = [(0, 0), (0, 0), (0, 0), (0, 0)]
                    for points_set in midpoints_history:
                        for i in range(4):
                            sum_midpoints[i] = (sum_midpoints[i][0] + points_set[i][0], sum_midpoints[i][1] + points_set[i][1])

                    avg_midpoints = []
                    for i in range(4):
                        avg_x = sum_midpoints[i][0] // AVERAGE_COUNT
                        avg_y = sum_midpoints[i][1] // AVERAGE_COUNT
                        avg_midpoints.append((avg_x, avg_y))

                    print("\n" + "="*50)
                    print("矩形检测完成！")
                    print(f"最终平均中点坐标:")
                    print(f"  - 左上:     {avg_midpoints[0]}")
                    print(f"  - 右上:     {avg_midpoints[1]}")
                    print(f"  - 右下:     {avg_midpoints[2]}")
                    print(f"  - 左下:     {avg_midpoints[3]}")
                    print("="*50 + "\n")

                    # 生成40个等分目标点（在中间框上）
                    all_target_points = generate_target_points(avg_midpoints)
                    if all_target_points:
                        print(f"生成{len(all_target_points)}个追踪目标点")
                        motion_active = True
                        current_target_index = 0
                        rectangle_detected = True

                        # 保存最终的内外框角点用于绘制
                        final_inner_points = first_corners
                        final_outer_points = second_corners
                        final_midpoints = midpoints
                        print("激光点追踪系统已激活！")
        except Exception as e:
            print(f"矩形处理异常: {e}")
            return []

    return []  # 如果没有检测到有效矩形，返回空列表

try:
    print("Camera Test - Averaging Rectangle Recognition")

    sensor = Sensor()
    sensor.reset()

    sensor.set_framesize(width=800, height=480)
    # 改为RGB888格式以支持cv_lite优化 / Change to RGB888 for cv_lite optimization
    sensor.set_pixformat(Sensor.RGB888)
    Display.init(Display.ST7701, to_ide=True, fps=20)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    button = Pin(53, Pin.IN, Pin.PULL_DOWN)

    fpioa = FPIOA()
    fpioa.set_function(11, FPIOA.UART2_TXD)
    fpioa.set_function(12, FPIOA.UART2_RXD)
    uart2 = UART(UART.UART2, 115200)

    fpioa.set_function(50, FPIOA.UART3_TXD)
    fpioa.set_function(51, FPIOA.UART3_RXD)
    uart3 = UART(UART.UART3, 115200)
    print("系统启动，开始自动检测矩形框...")

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        if not rectangle_detected:
            auto_detect_rectangle(img)

        #uart3_get = uart3.read()
        uart3_get = True
        if(contest_flow == 0):
            # ========================================
            # 使用cv_lite优化的色块检测 / Use cv_lite optimized blob detection
            # 性能提升：80fps vs 44fps (提升82%) / Performance: 80fps vs 44fps (82% improvement)
            # ========================================

            # 获取图像数据引用 / Get image data reference
            img_np = img.to_numpy_ref()
            image_shape = [480, 800]  # 高，宽 / Height, Width

            # 红色激光点检测 - 使用cv_lite优化 / Red laser detection with cv_lite optimization
            red_threshold = [66, 100, 4, 127, -10, 127]  # LAB颜色空间阈值 / LAB color space threshold
            min_area = 10  # 最小色块面积 / Minimum blob area
            kernel_size = 1  # 腐蚀膨胀核大小 / Kernel size for morphological ops

            # 调用cv_lite进行红色色块检测 / Call cv_lite for red blob detection
            red_blobs_data = cv_lite.rgb888_find_blobs(image_shape, img_np, red_threshold, min_area, kernel_size)

            # 绿色激光点检测 - 使用cv_lite优化 / Green laser detection with cv_lite optimization
            green_threshold = [63, 100, -96, -22, -11, 127]  # LAB颜色空间阈值 / LAB color space threshold

            # 调用cv_lite进行绿色色块检测 / Call cv_lite for green blob detection
            green_blobs_data = cv_lite.rgb888_find_blobs(image_shape, img_np, green_threshold, min_area, kernel_size)

            # 将cv_lite结果转换为兼容格式 / Convert cv_lite results to compatible format
            red_blobs = []
            if red_blobs_data and len(red_blobs_data) >= 4:
                for i in range(0, len(red_blobs_data), 4):
                    if i + 3 < len(red_blobs_data):
                        # 创建兼容的blob对象 / Create compatible blob object
                        class CVLiteBlob:
                            def __init__(self, x, y, w, h):
                                self._x, self._y, self._w, self._h = x, y, w, h
                            def x(self): return self._x
                            def y(self): return self._y
                            def w(self): return self._w
                            def h(self): return self._h

                        red_blobs.append(CVLiteBlob(red_blobs_data[i], red_blobs_data[i+1],
                                                   red_blobs_data[i+2], red_blobs_data[i+3]))

            green_blobs = []
            if green_blobs_data and len(green_blobs_data) >= 4:
                for i in range(0, len(green_blobs_data), 4):
                    if i + 3 < len(green_blobs_data):
                        green_blobs.append(CVLiteBlob(green_blobs_data[i], green_blobs_data[i+1],
                                                     green_blobs_data[i+2], green_blobs_data[i+3]))

            red_centerX = 0
            red_centerY = 0
            green_centerX = 0
            green_centerY = 0

            # 激光点坐标（用红色激光点作为追踪目标）
            rlaser_x = 0
            rlaser_y = 0
            glaser_x = 0
            glaser_y = 0
            # x_stride, y_stride 搜索步长
            # pixels_threshold 当识别的色块大于这个数值才会返回
            if red_blobs:
                current_blob = red_blobs[0]

                if prev_blob_data['x'] == -1: # First detection
                    smoothed_x = current_blob.x()
                    smoothed_y = current_blob.y()
                    smoothed_w = current_blob.w()
                    smoothed_h = current_blob.h()
                else:
                    smoothed_x = int(alpha * current_blob.x() + (1 - alpha) * prev_blob_data['x'])
                    smoothed_y = int(alpha * current_blob.y() + (1 - alpha) * prev_blob_data['y'])
                    smoothed_w = int(alpha * current_blob.w() + (1 - alpha) * prev_blob_data['w'])
                    smoothed_h = int(alpha * current_blob.h() + (1 - alpha) * prev_blob_data['h'])

                # Update previous blob data
                prev_blob_data['x'] = smoothed_x
                prev_blob_data['y'] = smoothed_y
                prev_blob_data['w'] = smoothed_w
                prev_blob_data['h'] = smoothed_h

                img.draw_rectangle(smoothed_x, smoothed_y, smoothed_w, smoothed_h, color=(0, 255, 0), thickness=1, fill=False)
                red_centerX = smoothed_x + smoothed_w / 2.0
                red_centerY = smoothed_y + smoothed_h / 2.0

                # 更新激光点坐标
                rlaser_x = int(red_centerX)
                rlaser_y = int(red_centerY)


            else:
                # Reset prev_blob_dSata if no blob is detected
                prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}

            if green_blobs:
                current_blob = green_blobs[0]

                if prev_blob_data['x'] == -1:
                    smoothed_x = current_blob.x()
                    smoothed_y = current_blob.y()
                    smoothed_w = current_blob.w()
                    smoothed_h = current_blob.h()
                else:
                    smoothed_x = int(alpha * current_blob.x() + (1 - alpha) * prev_blob_data['x'])
                    smoothed_y = int(alpha * current_blob.y() + (1 - alpha) * prev_blob_data['y'])
                    smoothed_w = int(alpha * current_blob.w() + (1 - alpha) * prev_blob_data['w'])
                    smoothed_h = int(alpha * current_blob.h() + (1 - alpha) * prev_blob_data['h'])

                prev_blob_data['x'] = smoothed_x
                prev_blob_data['y'] = smoothed_y
                prev_blob_data['w'] = smoothed_w
                prev_blob_data['h'] = smoothed_h

                img.draw_rectangle(smoothed_x, smoothed_y, smoothed_w, smoothed_h, color=(255, 0, 0), thickness=1, fill=False)
                green_centerX = smoothed_x + smoothed_w / 2.0
                green_centerY = smoothed_y + smoothed_h / 2.0

                glaser_x = int(green_centerX)
                glaser_y = int(green_centerY)

            else:
                # Reset prev_blob_dSata if no blob is detected
                prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}

            # 如果检测到激光点且有目标点，进行运动控制
            if rlaser_x > 0 and rlaser_y > 0 and len(all_target_points) > 0:
                #print("发送坐标差值到串口2")
                motion_red = red_motion(rlaser_x, rlaser_y)
                if motion_red:
                    # 在图像上绘制当前目标点
                    target_point = motion_red['target_point']
                    img.draw_circle(target_point[0], target_point[1], 5, color=(255, 0, 0), thickness=2, fill=False)

                    # 绘制激光点到目标点的连线
                    img.draw_line(rlaser_x, rlaser_y, target_point[0], target_point[1], color=(0, 255, 255), thickness=1)

                    print("({},{})({},{})".format(red_centerX, red_centerY, green_centerX, green_centerY))

                    # 调试信息：显示运动控制状态
                    print(
                        f"Debug: laser_detected=({rlaser_x},{rlaser_y}), targets_count={len(all_target_points)}, current_target={current_target_index}")

                    #print(f"Target: {motion_red['target_index']}/40, Distance: {motion_red['distance']:.1f}, Diff: {motion_info['diff']}")
            # else:
            #     # 如果没有检测到激光点或没有目标点，不发送串口2数据
            #     print("未检测到激光点或无目标点，不发送串口2数据")

            if uart3_get == True:
                if rlaser_x > 0 and rlaser_y > 0 and glaser_x > 0 and glaser_y > 0:
                    print("开始绿色光追踪红色光")
                    motion_green = green_motion(rlaser_x, rlaser_y,glaser_x,glaser_y)
                # if motion_green:
                #
                #     img.draw_circle(target_point[0], target_point[1], 5, color=(255, 0, 0), thickness=2, fill=False)
                #     # 绘制激光点到目标点的连线
                #     img.draw_line(rlaser_x, rlaser_y, target_point[0], target_point[1], color=(0, 255, 255), thickness=1)

            #只通过uart3发送调试信息，uart2专门用于协议数据
            #uart3.write("({},{})({},{})\r\n".format(red_centerX, red_centerY, green_centerX, green_centerY))

        # 绘制ROI区域
        img.draw_rectangle(roi, color=(0, 0, 255), thickness=1, fill=False)

        # 矩形检测完成后，绘制固定的内外框轮廓
        if rectangle_detected and final_inner_points and final_outer_points and final_midpoints:
            # 绿色内框
            inner_sorted = sort_corners_by_position(final_inner_points)
            for i in range(4):
                x1, y1 = inner_sorted[i]
                x2, y2 = inner_sorted[(i + 1) % 4]
                img.draw_line(x1, y1, x2, y2, color=(0, 255, 0), thickness=2)
                img.draw_circle(x1, y1, 2, color=(0, 255, 0), thickness=2, fill=True)

            # 蓝色外框
            outer_sorted = sort_corners_by_position(final_outer_points)
            for i in range(4):
                x1, y1 = outer_sorted[i]
                x2, y2 = outer_sorted[(i + 1) % 4]
                img.draw_line(x1, y1, x2, y2, color=(0, 0, 255), thickness=2)
                img.draw_circle(x1, y1, 2, color=(0, 0, 255), thickness=2, fill=True)


            # 绘制中间框（紫色）- 使用avg_midpoints四个点画出紫色终点框
            # 中间框由内外框对应角点的中点组成
            if len(final_midpoints) == 4:
                for i in range(4):
                    x1, y1 = final_midpoints[i]
                    x2, y2 = final_midpoints[(i + 1) % 4]
                    img.draw_line(x1, y1, x2, y2, color=(255, 0, 255), thickness=2)  # 紫色终点框

                # 绘制所有40个目标点
                for i, point in enumerate(all_target_points):
                    img.draw_circle(point[0], point[1], 2, color=(255, 255, 0), thickness=1, fill=True)

                # 标注四个角点
                #corner_labels = ["左上", "右上", "右下", "左下"]
                #for i, corner in enumerate(middle_frame_corners):
                    #img.draw_circle(corner[0], corner[1], 5, color=(255, 0, 0), thickness=2, fill=False)

        #img.draw_string_advanced(30, 30, 50, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        Display.show_image(img)
except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()

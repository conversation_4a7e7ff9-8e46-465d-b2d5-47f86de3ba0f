# ============================================================
# MicroPython 基于 cv_lite 的 RGB888 矩形检测测试代码
# RGB888 Rectangle Detection Test using cv_lite extension
# ============================================================

import time, os, sys, gc
from machine import Pin
from media.sensor import *     # 摄像头接口 / Camera interface
from media.display import *    # 显示接口 / Display interface
from media.media import *      # 媒体资源管理器 / Media manager
import _thread
import cv_lite                 # cv_lite扩展模块 / cv_lite extension module
import ulab.numpy as np

# -------------------------------
# 图像尺寸 [高, 宽] / Image size [Height, Width]
# -------------------------------
image_shape = [480, 640]

# -------------------------------
# 初始化摄像头（RGB888格式） / Initialize camera (RGB888 format)
# -------------------------------
sensor = Sensor(id=2, width=image_shape[1], height=image_shape[0])
sensor.reset()
sensor.set_framesize(width=image_shape[1], height=image_shape[0])
sensor.set_pixformat(Sensor.RGB888)

# -------------------------------
# 初始化显示器（LCD屏幕显示） / Initialize display (LCD screen output)
# -------------------------------
# 显示模式选择：可以是 "VIRT"、"LCD" 或 "HDMI"
DISPLAY_MODE = "LCD"  # 改为LCD模式显示到3.1寸屏幕

# 根据模式设置显示宽高
if DISPLAY_MODE == "VIRT":
    # 虚拟显示器模式（IDE调试用）
    DISPLAY_WIDTH = 640
    DISPLAY_HEIGHT = 480
    Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
elif DISPLAY_MODE == "LCD":
    # 3.1寸屏幕模式（立创·3.1寸屏幕扩展板）
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
elif DISPLAY_MODE == "HDMI":
    # HDMI扩展板模式（立创·MIPI转HDMI扩展板）
    DISPLAY_WIDTH = 1920
    DISPLAY_HEIGHT = 1080
    Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
else:
    raise ValueError("未知的 DISPLAY_MODE，请选择 'VIRT', 'LCD' 或 'HDMI'")

# 注意：to_ide=True 可以同时在IDE和LCD上显示，方便调试

# -------------------------------
# 初始化媒体资源管理器并启动摄像头 / Init media manager and start camera
# -------------------------------
MediaManager.init()
sensor.run()

# -------------------------------
# 启动帧率计时器 / Start FPS timer
# -------------------------------
clock = time.clock()

# -------------------------------
# 可调参数（建议调试时调整）/ Adjustable parameters (recommended for tuning)
# -------------------------------
# 🔧 矩形检测参数调优指南 / Rectangle Detection Parameter Tuning Guide:
#
# 1. canny_thresh1/2: 边缘检测敏感度，值越小检测越敏感
#    - 检测不到矩形：降低阈值 (30, 100)
#    - 检测到太多噪声：提高阈值 (80, 200)
#
# 2. approx_epsilon: 多边形拟合精度，值越小越精确
#    - 矩形不够精确：降低值 (0.02)
#    - 检测过于严格：提高值 (0.06)
#
# 3. area_min_ratio: 最小面积过滤，避免检测小噪声
#    - 小矩形检测不到：降低值 (0.0005)
#    - 噪声太多：提高值 (0.005)
#
# 4. max_angle_cos: 角度严格度，值越小要求越接近90度
#    - 检测不到略变形的矩形：提高值 (0.7)
#    - 检测到非矩形：降低值 (0.3)

canny_thresh1       = 50        # Canny 边缘检测低阈值 / Canny edge low threshold
canny_thresh2       = 150       # Canny 边缘检测高阈值 / Canny edge high threshold
approx_epsilon      = 0.04      # 多边形拟合精度（比例） / Polygon approximation precision (ratio)
area_min_ratio      = 0.001     # 最小面积比例（0~1） / Minimum area ratio (0~1)
max_angle_cos       = 0.5       # 最大角余弦（值越小越接近矩形） / Max cosine of angle (smaller closer to rectangle)
gaussian_blur_size  = 5         # 高斯模糊核大小（奇数） / Gaussian blur kernel size (odd number)

print("🚀 cv_lite RGB888矩形检测启动 / cv_lite RGB888 Rectangle Detection Started")
print(f"📺 显示模式: {DISPLAY_MODE} ({DISPLAY_WIDTH}x{DISPLAY_HEIGHT})")
print(f"🎯 检测参数: Canny({canny_thresh1},{canny_thresh2}) | Epsilon({approx_epsilon}) | MinArea({area_min_ratio})")
print("=" * 80)

# -------------------------------
# 性能统计变量 / Performance statistics variables
# -------------------------------
frame_count = 0
total_rects_detected = 0
max_rects_in_frame = 0

# -------------------------------
# 主循环 / Main loop
# -------------------------------
try:
    while True:
        clock.tick()
        frame_count += 1

        # 拍摄当前帧图像 / Capture current frame
        img = sensor.snapshot()
        img_np = img.to_numpy_ref()  # 获取 RGB888 ndarray 引用 / Get RGB888 ndarray reference

        # ========================================
        # cv_lite矩形检测 / cv_lite rectangle detection
        # ========================================
        try:
            # 调用底层矩形检测函数，返回矩形列表 [x0, y0, w0, h0, x1, y1, w1, h1, ...]
            # Call underlying rectangle detection function, returns list of rectangles [x, y, w, h, ...]
            detection_start = time.ticks_ms()

            rects = cv_lite.rgb888_find_rectangles(
                image_shape, img_np,
                canny_thresh1, canny_thresh2,
                approx_epsilon,
                area_min_ratio,
                max_angle_cos,
                gaussian_blur_size
            )

            detection_time = time.ticks_diff(time.ticks_ms(), detection_start)

        except Exception as e:
            print(f"❌ cv_lite检测错误: {e}")
            rects = []
            detection_time = 0

    # ========================================
    # 遍历检测到的矩形，进行详细标记 / Iterate detected rectangles and draw detailed markers
    # ========================================
    rect_count = 0
    for i in range(0, len(rects), 4):
        x = rects[i]
        y = rects[i + 1]
        w = rects[i + 2]
        h = rects[i + 3]
        rect_count += 1

        # 1. 绘制矩形框（蓝色边框） / Draw rectangle border (blue)
        img.draw_rectangle(x, y, w, h, color=(0, 0, 255), thickness=3)

        # 2. 计算矩形的四个顶点坐标 / Calculate four corner coordinates
        top_left = (x, y)                    # 左上角 / Top-left
        top_right = (x + w, y)               # 右上角 / Top-right
        bottom_left = (x, y + h)             # 左下角 / Bottom-left
        bottom_right = (x + w, y + h)        # 右下角 / Bottom-right

        # 3. 用红点标记四个顶点 / Mark four corners with red dots
        dot_radius = 6  # 红点半径 / Red dot radius
        img.draw_circle(top_left[0], top_left[1], dot_radius, color=(255, 0, 0), thickness=-1)      # 左上角红点
        img.draw_circle(top_right[0], top_right[1], dot_radius, color=(255, 0, 0), thickness=-1)    # 右上角红点
        img.draw_circle(bottom_left[0], bottom_left[1], dot_radius, color=(255, 0, 0), thickness=-1) # 左下角红点
        img.draw_circle(bottom_right[0], bottom_right[1], dot_radius, color=(255, 0, 0), thickness=-1) # 右下角红点

        # 4. 计算矩形中心点 / Calculate rectangle center
        center_x = x + w // 2
        center_y = y + h // 2

        # 5. 绘制十字架标记中心点（绿色） / Draw cross to mark center (green)
        cross_size = 15  # 十字架大小 / Cross size
        cross_thickness = 3  # 十字架线条粗细 / Cross line thickness

        # 绘制水平线 / Draw horizontal line
        img.draw_line(center_x - cross_size, center_y, center_x + cross_size, center_y,
                     color=(0, 255, 0), thickness=cross_thickness)
        # 绘制垂直线 / Draw vertical line
        img.draw_line(center_x, center_y - cross_size, center_x, center_y + cross_size,
                     color=(0, 255, 0), thickness=cross_thickness)

        # 6. 添加矩形信息文本 / Add rectangle info text
        info_text = f"Rect{rect_count}: {w}x{h}"
        img.draw_string_advanced(x, y - 25, 20, info_text, color=(255, 255, 0))  # 黄色文字

        # 7. 添加中心点坐标文本 / Add center coordinates text
        center_text = f"({center_x},{center_y})"
        img.draw_string_advanced(center_x - 40, center_y + 20, 16, center_text, color=(255, 255, 255))  # 白色文字

    # ========================================
    # 添加整体信息显示 / Add overall information display
    # ========================================

    # 在图像顶部显示检测统计信息 / Display detection statistics at top
    fps_text = f"FPS: {clock.fps():.1f}"
    rect_count_text = f"Rectangles: {rect_count}"
    resolution_text = f"Resolution: {image_shape[1]}x{image_shape[0]}"

    # 显示帧率信息（左上角，绿色） / Display FPS info (top-left, green)
    img.draw_string_advanced(10, 10, 24, fps_text, color=(0, 255, 0))

    # 显示矩形数量（左上角第二行，青色） / Display rectangle count (second line, cyan)
    img.draw_string_advanced(10, 40, 20, rect_count_text, color=(0, 255, 255))

    # 显示分辨率信息（左上角第三行，白色） / Display resolution (third line, white)
    img.draw_string_advanced(10, 65, 16, resolution_text, color=(255, 255, 255))

    # 显示cv_lite优化标识（右上角，黄色） / Display cv_lite optimization label (top-right, yellow)
    optimization_text = "CV_LITE Optimized"
    img.draw_string_advanced(image_shape[1] - 200, 10, 18, optimization_text, color=(255, 255, 0))

    # ========================================
    # 显示处理后的图像到LCD / Display processed image to LCD
    # ========================================
    Display.show_image(img)

    # 释放临时变量内存 / Free temporary variables memory
    del img_np
    del img

    # 进行垃圾回收 / Perform garbage collection
    gc.collect()

    # ========================================
    # 更新性能统计 / Update performance statistics
    # ========================================
    total_rects_detected += rect_count
    if rect_count > max_rects_in_frame:
        max_rects_in_frame = rect_count

    # 打印详细的检测信息到控制台 / Print detailed detection info to console
    if frame_count % 30 == 0:  # 每30帧打印一次统计信息 / Print stats every 30 frames
        avg_rects = total_rects_detected / frame_count if frame_count > 0 else 0
        print(f"📊 Frame {frame_count} | FPS: {clock.fps():.2f} | Current: {rect_count} rects | "
              f"Avg: {avg_rects:.1f} | Max: {max_rects_in_frame} | Detection: {detection_time}ms")
    else:
        print(f"FPS: {clock.fps():.2f} | Rects: {rect_count} | Detection: {detection_time}ms")

except KeyboardInterrupt:
    print("\n🛑 用户中断程序 / User interrupted program")
except Exception as e:
    print(f"\n❌ 程序异常: {e}")
finally:
    # -------------------------------
    # 退出时释放资源和显示统计信息 / Cleanup on exit and show statistics
    # -------------------------------
    print("\n" + "=" * 80)
    print("📈 最终统计信息 / Final Statistics:")
    print(f"🎬 总帧数: {frame_count}")
    print(f"🔍 总检测矩形数: {total_rects_detected}")
    print(f"📊 平均每帧矩形数: {total_rects_detected/frame_count if frame_count > 0 else 0:.2f}")
    print(f"🏆 单帧最大矩形数: {max_rects_in_frame}")
    print(f"⚡ 平均帧率: {frame_count/clock.avg() if clock.avg() > 0 else 0:.2f} FPS")
    print("=" * 80)

    try:
        sensor.stop()
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        MediaManager.deinit()
        print("✅ 资源释放完成 / Resource cleanup completed")
    except:
        print("⚠️ 资源释放时出现问题 / Issues during resource cleanup")
